import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useChat } from '../../../contexts/ChatContext';
import { useAuth } from '../../../contexts/AuthContext';
import Sidebar from '../Sidebar/Sidebar';
import ChatArea from '../ChatArea/ChatArea';
import SignupModal from '../../common/SignupModal';
import FileUploadLimitModal from '../../common/FileUploadLimitModal';
import ProjectModal from '../ProjectModal/ProjectModal';
import googleAuthService from '../../../services/googleAuthService';
import appsData from '../../../data/apps.json';
import './ChatLayout.css';

const ChatLayout = ({ threadId }) => {
  // Check if mobile on initial load
  const isMobile = () => window.innerWidth <= 768;
  const [isSidebarOpen, setIsSidebarOpen] = useState(!isMobile());
  const [showProjectModal, setShowProjectModal] = useState(false);
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();
  const {
    selectThreadById,
    setNavigationCallback,
    setCurrentApp,
    currentApp,
    showSignupModal,
    signupModalMessage,
    closeSignupModal,
    showFileUploadLimitModal,
    fileUploadLimitMessage,
    closeFileUploadLimitModal
  } = useChat();
  const lastLoadedThreadId = useRef(null);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Create stable navigation callback
  const navigationCallback = useCallback((url) => {
    navigate(url, { replace: true });
  }, [navigate]);

  // Set navigation callback for ChatContext
  useEffect(() => {
    setNavigationCallback(() => navigationCallback);
  }, [navigationCallback, setNavigationCallback]);

  // Handle thread ID from URL - wait for authentication to complete
  useEffect(() => {
    // Don't try to load thread if authentication is still loading
    if (isLoading) return;

    if (threadId) {
      // Check if threadId is actually an app ID
      const allApps = [...appsData.store];
      const isAppId = allApps.some(app => app.id === threadId);

      if (isAppId) {
        // Set app context for app-specific chat
        setCurrentApp(threadId);
        lastLoadedThreadId.current = null; // Clear thread context
      } else if (threadId !== lastLoadedThreadId.current && isAuthenticated) {
        // Load the specific thread if threadId is provided in URL and we haven't loaded it yet
        lastLoadedThreadId.current = threadId;
        setCurrentApp(null); // Clear app context
        selectThreadById(threadId);
      }
    } else {
      // Clear both thread and app context when there's no threadId
      lastLoadedThreadId.current = null;
      setCurrentApp(null);
    }
  }, [threadId, selectThreadById, setCurrentApp, isAuthenticated, isLoading]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 768) {
        // Desktop: sidebar should be open by default
        setIsSidebarOpen(true);
      } else {
        // Mobile: sidebar should be closed by default
        setIsSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Check Google Auth status when Google Drive utility app is loaded
  useEffect(() => {
    const checkGoogleAuthStatus = async () => {
      if (currentApp === 'google-drive-utility') {
        try {
          console.log('Checking Google Auth status for Google Drive utility...');
          const result = await googleAuthService.getAuthStatusWithRefresh();

          if (result.tokenRefreshInitiated) {
            console.log('Google Auth token refresh was initiated');
            // Optionally show a notification to the user that token was refreshed
            if (result.refreshResponse?.data?.authUrl) {
              console.log('New auth URL available for token refresh:', result.refreshResponse.data.authUrl);
              // You could optionally redirect the user to complete the refresh
              // window.open(result.refreshResponse.data.authUrl, '_blank', 'noopener,noreferrer');
            }
          } else if (result.success && result.data?.isAuthenticated) {
            console.log('Google Auth status is valid');
          } else {
            console.log('Google Auth is not authenticated');
          }
        } catch (error) {
          console.error('Error checking Google Auth status:', error);
        }
      }
    };

    // Only check auth status if not loading and currentApp is set
    if (!isLoading && currentApp === 'google-drive-utility') {
      checkGoogleAuthStatus();
    }
  }, [currentApp, isLoading]);

  return (
    <div className="chat-layout">
      <Sidebar
        isOpen={isSidebarOpen}
        onToggle={toggleSidebar}
        onCreateProject={() => setShowProjectModal(true)}
      />
      <ChatArea isSidebarOpen={isSidebarOpen} onToggleSidebar={toggleSidebar} />

      {/* Mobile overlay */}
      {isSidebarOpen && (
        <div
          className="chat-layout__overlay"
          onClick={toggleSidebar}
        />
      )}

      {/* Project Modal - Outside sidebar for proper mobile positioning */}
      <ProjectModal
        isOpen={showProjectModal}
        onClose={() => setShowProjectModal(false)}
      />

      {/* Signup Modal */}
      <SignupModal
        isOpen={showSignupModal}
        onClose={closeSignupModal}
        message={signupModalMessage}
      />

      {/* File Upload Limit Modal */}
      <FileUploadLimitModal
        isOpen={showFileUploadLimitModal}
        onClose={closeFileUploadLimitModal}
        message={fileUploadLimitMessage}
      />
    </div>
  );
};

export default ChatLayout;
