import apiClient from './apiClient';

class GoogleAuthService {
  /**
   * Check Google authentication status
   * @returns {Promise<Object>} Authentication status response
   */
  async getAuthStatus() {
    try {
      const response = await apiClient.get('/google-auth/status');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get available Google services and their scopes
   * @returns {Promise<Object>} Available services response
   */
  async getAvailableServices() {
    try {
      const response = await apiClient.get('/google-auth/services');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Initiate Google OAuth authentication
   * @param {string[]} scopes - Array of OAuth scopes to request
   * @returns {Promise<Object>} Authentication initiation response
   */
  async initiateAuthentication(scopes) {
    try {
      const response = await apiClient.post('/google-auth/initiate', {
        scopes
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Check if user is authenticated with Google
   * @returns {Promise<boolean>} Authentication status
   */
  async isAuthenticated() {
    try {
      const statusResponse = await this.getAuthStatus();
      return statusResponse.success && statusResponse.data?.isAuthenticated;
    } catch (error) {
      console.error('Error checking Google authentication status:', error);
      return false;
    }
  }

  /**
   * Get user's Google account information
   * @returns {Promise<Object|null>} User info or null if not authenticated
   */
  async getUserInfo() {
    try {
      const statusResponse = await this.getAuthStatus();
      if (statusResponse.success && statusResponse.data?.isAuthenticated) {
        return statusResponse.data.userInfo;
      }
      return null;
    } catch (error) {
      console.error('Error getting Google user info:', error);
      return null;
    }
  }

  /**
   * Start the complete authentication flow
   * This method handles the entire flow: check status -> get services -> initiate auth
   * @returns {Promise<Object>} Result of the authentication flow
   */
  async startAuthenticationFlow() {
    try {
      // Step 1: Check current authentication status
      const statusResponse = await this.getAuthStatus();
      
      if (statusResponse.success && statusResponse.data?.isAuthenticated) {
        // User is already authenticated
        return {
          success: true,
          authenticated: true,
          userInfo: statusResponse.data.userInfo,
          message: 'User is already authenticated with Google'
        };
      }

      // Step 2: User is not authenticated, get available services
      const servicesResponse = await this.getAvailableServices();
      
      if (!servicesResponse.success) {
        throw new Error('Failed to retrieve Google services');
      }

      // Step 3: Initiate authentication with default scopes
      const defaultScopes = servicesResponse.data?.defaultScopes || [];
      const authResponse = await this.initiateAuthentication(defaultScopes);

      return {
        success: true,
        authenticated: false,
        authUrl: authResponse.data?.authUrl,
        scopes: defaultScopes,
        message: 'Authentication initiated successfully'
      };

    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Handle API errors consistently
   * @param {Error} error - The error object
   * @returns {Error} Formatted error
   */
  handleError(error) {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message);
    }
    return new Error(error.message || 'An unexpected error occurred with Google authentication');
  }
}

// Create singleton instance
const googleAuthService = new GoogleAuthService();
export default googleAuthService;
